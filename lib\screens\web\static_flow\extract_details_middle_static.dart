import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class ExtractDetailsMiddleStatic extends StatelessWidget {
  const ExtractDetailsMiddleStatic({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            provider.isAIMode ? 'Extracted Details' : 'Objects',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.w400,
              height: 1,
            ),
          ),

          // AI/Manual Toggle - positioned to the right
          _buildAIManualToggle(context, provider),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              provider.toggleAIMode();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          provider.isAIMode ? 'Form' : 'Manually Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (provider.isAIMode) {
      return _buildExtractedDetailsTab(context);
    } else {
      return _buildObjectsTab(context);
    }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Expansion panels for objects in extracted details
          _buildObjectExpansionPanel(context, 'Object: Customer'),
          _buildObjectExpansionPanel(context, 'Object: Product'),
          _buildObjectExpansionPanel(context, 'Object: Order'),
        ],
      ),
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Simple content items for objects mode
          _buildContentItem(
            context,
            'Policy has Policy ID - Policy Number, Product',
            'Policy has Policy ID - Policy Number, Product',
          ),

          const SizedBox(height: 16),

          _buildContentItem(
            context,
            'Customer has Customer ID - Name, email',
            'Customer has Customer ID - Name, email',
          ),

          const SizedBox(height: 16),

          _buildContentItem(
            context,
            'Product has product ID - Product Number',
            'Product has product ID - Product Number',
          ),
        ],
      ),
    );
  }

  Widget _buildContentItem(
      BuildContext context, String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Color(0xFF0058FF),
              shape: BoxShape.circle,
            ),
          ),

          const SizedBox(width: 12),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                if (subtitle != title) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        childrenPadding: EdgeInsets.zero,
        title: Row(
          children: [
            Text(
              objectTitle,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
                fontWeight: FontWeight.w500,
                height: 1,
              ),
            ),
            const Spacer(),
            // Alert icon
            Container(
              padding: const EdgeInsets.all(2),
              child: Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 16,
              ),
            ),
            // Blue expand icon
          ],
        ),
        children: [
          // Empty content - no nested accordion
          Container(
            height: 50,
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Text(
                'Content will be added here',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[500]!,
                  fontWeight: FontWeight.w400,
                  height: 1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
